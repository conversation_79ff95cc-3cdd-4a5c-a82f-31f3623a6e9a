using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Linq;

namespace Business.Concrete
{
    public class SalonManager : ISalonService
    {
        private readonly ICompanyDal _companyDal;
        private readonly ICompanyAdressDal _companyAdressDal;
        private readonly ICompanyUserDal _companyUserDal;
        private readonly IUserCompanyDal _userCompanyDal;
        private readonly IUserDal _userDal;

        public SalonManager(
            ICompanyDal companyDal,
            ICompanyAdressDal companyAdressDal,
            ICompanyUserDal companyUserDal,
            IUserCompanyDal userCompanyDal,
            IUserDal userDal)
        {
            _companyDal = companyDal;
            _companyAdressDal = companyAdressDal;
            _companyUserDal = companyUserDal;
            _userCompanyDal = userCompanyDal;
            _userDal = userDal;
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(SalonCreateValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult CreateSalonWithOwner(SalonCreateDto salonCreateDto)
        {
            try
            {
                // 1. Ad-Soyad Parse Et
                var (firstName, lastName) = ParseFullName(salonCreateDto.OwnerFullName);

                // 2. Geçici Şifre Oluştur
                var tempPassword = GenerateTempPassword(salonCreateDto.OwnerPhone);

                // 3. User Oluştur
                var user = CreateUser(firstName, lastName, salonCreateDto.OwnerEmail, tempPassword);
                _userDal.Add(user);

                // 4. Company Oluştur
                var company = CreateCompany(salonCreateDto);
                _companyDal.Add(company);

                // 5. CompanyAddress Oluştur
                var companyAddress = CreateCompanyAddress(salonCreateDto, company.CompanyID);
                _companyAdressDal.Add(companyAddress);

                // 6. CompanyUser Oluştur
                var companyUser = CreateCompanyUser(salonCreateDto, user.UserID);
                _companyUserDal.Add(companyUser);

                // 7. UserCompany İlişkisi Oluştur
                var userCompany = CreateUserCompanyRelation(user.UserID, company.CompanyID);
                _userCompanyDal.Add(userCompany);

                // 8. Hoş Geldin Maili (Console Log)
                if (salonCreateDto.SendWelcomeEmail)
                {
                    LogWelcomeMessage(salonCreateDto, tempPassword);
                }

                return new SuccessResult($"Salon '{salonCreateDto.CompanyName}' başarıyla oluşturuldu. Şirket sahibi: {salonCreateDto.OwnerFullName}");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon oluşturulurken hata oluştu: {ex.Message}");
            }
        }

        private (string firstName, string lastName) ParseFullName(string fullName)
        {
            var parts = fullName.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            
            if (parts.Length < 2)
                throw new ArgumentException("Ad ve soyad en az 2 kelime olmalıdır");
            
            var lastName = parts[parts.Length - 1];
            var firstName = string.Join(" ", parts.Take(parts.Length - 1));
            
            return (firstName, lastName);
        }

        private string GenerateTempPassword(string phoneNumber)
        {
            // Telefon numarasının son 4 hanesi
            var cleanPhone = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            if (cleanPhone.Length < 4)
                throw new ArgumentException("Telefon numarası en az 4 haneli olmalıdır");
            
            return cleanPhone.Substring(cleanPhone.Length - 4);
        }

        private User CreateUser(string firstName, string lastName, string email, string tempPassword)
        {
            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

            return new User
            {
                FirstName = firstName,
                LastName = lastName,
                Email = email,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsActive = true,
                RequirePasswordChange = true, // İlk girişte şifre değiştirme zorunlu
                CreationDate = DateTime.Now
            };
        }

        private Company CreateCompany(SalonCreateDto dto)
        {
            return new Company
            {
                CompanyName = dto.CompanyName,
                PhoneNumber = dto.CompanyPhone,
                IsActive = true,
                CreationDate = DateTime.Now
            };
        }

        private CompanyAdress CreateCompanyAddress(SalonCreateDto dto, int companyId)
        {
            return new CompanyAdress
            {
                CompanyID = companyId,
                CityID = dto.CityId,
                TownID = dto.TownId,
                Adress = dto.Address,
                IsActive = true,
                CreationDate = DateTime.Now
            };
        }

        private CompanyUser CreateCompanyUser(SalonCreateDto dto, int userId)
        {
            return new CompanyUser
            {
                CityID = dto.CityId,
                TownID = dto.TownId,
                Name = dto.OwnerFullName, // Tam ad CompanyUser'da saklanır
                PhoneNumber = dto.OwnerPhone,
                Email = dto.OwnerEmail,
                IsActive = true,
                CreationDate = DateTime.Now
            };
        }

        private UserCompany CreateUserCompanyRelation(int userId, int companyId)
        {
            return new UserCompany
            {
                UserID = userId,
                CompanyId = companyId,
                IsActive = true,
                CreationDate = DateTime.Now
            };
        }

        private void LogWelcomeMessage(SalonCreateDto dto, string tempPassword)
        {
            Console.WriteLine("=== HOŞ GELDİN MAİLİ ===");
            Console.WriteLine($"Alıcı: {dto.OwnerEmail}");
            Console.WriteLine($"Konu: {dto.CompanyName} - Sisteme Hoş Geldiniz!");
            Console.WriteLine($"Şirket Sahibi: {dto.OwnerFullName}");
            Console.WriteLine($"Şirket: {dto.CompanyName}");
            Console.WriteLine($"Email: {dto.OwnerEmail}");
            Console.WriteLine($"Geçici Şifre: {tempPassword}");
            Console.WriteLine($"Not: İlk girişte şifre değiştirme zorunludur.");
            Console.WriteLine("========================");
        }
    }
}
