<!-- Loading Overlay -->
<div *ngIf="isSubmitting" class="loading-overlay">
  <div class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-3">Salon oluşturuluyor...</p>
  </div>
</div>

<!-- Main Container -->
<div class="container-fluid py-4" [class.content-blur]="isSubmitting">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h4 class="mb-1">
                <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
                <PERSON>ni <PERSON>ıt Sistemi
              </h4>
              <p class="text-muted mb-0">{{ getStepTitle() }}</p>
            </div>
            <div class="d-flex gap-2">
              <button
                *ngIf="currentStep > SalonWizardStep.COMPANY_INFO"
                type="button"
                class="modern-btn modern-btn-secondary"
                [disabled]="isSubmitting"
                (click)="previousStep()">
                <fa-icon [icon]="faArrowLeft" class="modern-btn-icon"></fa-icon>
                Önceki
              </button>
              <button
                *ngIf="currentStep < SalonWizardStep.PREVIEW"
                type="button"
                class="modern-btn modern-btn-primary"
                [disabled]="isSubmitting"
                (click)="nextStep()">
                Sonraki
                <fa-icon [icon]="faArrowRight" class="modern-btn-icon"></fa-icon>
              </button>
              <button
                *ngIf="currentStep === SalonWizardStep.PREVIEW"
                type="button"
                class="modern-btn modern-btn-success"
                [disabled]="isSubmitting || !canSubmit()"
                (click)="onSubmit()">
                <fa-icon [icon]="faSave" class="modern-btn-icon"></fa-icon>
                <span *ngIf="!isSubmitting">Salonu Kaydet</span>
                <span *ngIf="isSubmitting">Kaydediliyor...</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Step Navigation -->
  <div class="modern-card mb-4">
    <div class="modern-card-body py-3">
      <div class="row g-2">
        <div class="col" *ngFor="let stepInfo of wizardSteps">
          <button
            type="button"
            class="btn w-100 text-start"
            [class]="getStepButtonClass(stepInfo.step)"
            [disabled]="!canGoToStep(stepInfo.step) || isSubmitting"
            (click)="goToStep(stepInfo.step)">
            <div class="d-flex align-items-center">
              <div class="me-2">
                <fa-icon [icon]="getStepIcon(stepInfo.step)"></fa-icon>
              </div>
              <div>
                <div class="fw-bold">{{ stepInfo.step }}. {{ stepInfo.title }}</div>
                <small class="text-muted">{{ stepInfo.description }}</small>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 1: Company Information -->
  <div *ngIf="currentStep === SalonWizardStep.COMPANY_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
        Şirket Temel Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="companyInfoForm">
        <div class="row g-3">
          <!-- Company Name -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Şirket Adı <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(companyInfoForm, 'companyName')"
                formControlName="companyName"
                placeholder="Örn: Fitness Center Ankara">
              <div class="invalid-feedback" *ngIf="isFieldInvalid(companyInfoForm, 'companyName')">
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('required')">
                  Şirket adı zorunludur
                </div>
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('minlength')">
                  Şirket adı en az 2 karakter olmalıdır
                </div>
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('maxlength')">
                  Şirket adı en fazla 100 karakter olabilir
                </div>
              </div>
            </div>
          </div>

          <!-- Company Phone -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Şirket Telefonu <span class="text-danger">*</span>
              </label>
              <input
                type="tel"
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(companyInfoForm, 'companyPhone')"
                formControlName="companyPhone"
                placeholder="05xxxxxxxxx">
              <div class="invalid-feedback" *ngIf="isFieldInvalid(companyInfoForm, 'companyPhone')">
                <div *ngIf="companyInfoForm.get('companyPhone')?.hasError('required')">
                  Telefon numarası zorunludur
                </div>
                <div *ngIf="companyInfoForm.get('companyPhone')?.hasError('pattern')">
                  Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 2: Address Information -->
  <div *ngIf="currentStep === SalonWizardStep.ADDRESS_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
        Adres Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="addressInfoForm">
        <div class="row g-3">
          <!-- City -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                İl <span class="text-danger">*</span>
              </label>
              <mat-form-field appearance="outline" class="modern-mat-form-field">
                <mat-label>İl seçiniz</mat-label>
                <input matInput formControlName="city" [matAutocomplete]="autoCity" placeholder="İl ara...">
                <mat-icon matPrefix>location_city</mat-icon>
                <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity">
                  <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                    {{ city.cityName }}
                  </mat-option>
                </mat-autocomplete>
                <mat-error *ngIf="addressInfoForm.get('city')?.hasError('required')">
                  İl seçimi zorunludur
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Town -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                İlçe <span class="text-danger">*</span>
              </label>
              <mat-form-field appearance="outline" class="modern-mat-form-field">
                <mat-label>İlçe seçiniz</mat-label>
                <input matInput formControlName="town" [matAutocomplete]="autoTown" placeholder="İlçe ara...">
                <mat-icon matPrefix>location_on</mat-icon>
                <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                  <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                    {{ town.townName }}
                  </mat-option>
                </mat-autocomplete>
                <mat-error *ngIf="addressInfoForm.get('town')?.hasError('required')">
                  İlçe seçimi zorunludur
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Address -->
          <div class="col-12">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Detay Adres <span class="text-danger">*</span>
              </label>
              <textarea
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(addressInfoForm, 'address')"
                formControlName="address"
                rows="3"
                placeholder="Mahalle, sokak, bina no, kat, daire no vb. detayları giriniz..."></textarea>
              <div class="invalid-feedback" *ngIf="isFieldInvalid(addressInfoForm, 'address')">
                <div *ngIf="addressInfoForm.get('address')?.hasError('required')">
                  Adres zorunludur
                </div>
                <div *ngIf="addressInfoForm.get('address')?.hasError('minlength')">
                  Adres en az 10 karakter olmalıdır
                </div>
                <div *ngIf="addressInfoForm.get('address')?.hasError('maxlength')">
                  Adres en fazla 500 karakter olabilir
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 3: Owner Information -->
  <div *ngIf="currentStep === SalonWizardStep.OWNER_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
        Şirket Sahibi Bilgileri
      </h5>
      <p class="mb-0 mt-2 text-muted">
        <small>Şirket sahibi için otomatik kullanıcı hesabı oluşturulacaktır</small>
      </p>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="ownerInfoForm">
        <div class="row g-3">
          <!-- Owner Full Name -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Ad Soyad <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(ownerInfoForm, 'ownerFullName')"
                formControlName="ownerFullName"
                placeholder="Örn: Ahmet Mehmet Kaya">
              <small class="form-text text-muted">
                Son kelime soyad, diğerleri ad olarak kaydedilecektir
              </small>
              <div class="invalid-feedback" *ngIf="isFieldInvalid(ownerInfoForm, 'ownerFullName')">
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('required')">
                  Ad soyad zorunludur
                </div>
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('minlength')">
                  Ad soyad en az 3 karakter olmalıdır
                </div>
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('maxlength')">
                  Ad soyad en fazla 100 karakter olabilir
                </div>
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('fullName')">
                  Ad ve soyad en az 2 kelime olmalıdır
                </div>
              </div>
            </div>
          </div>

          <!-- Owner Phone -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Telefon Numarası <span class="text-danger">*</span>
              </label>
              <input
                type="tel"
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(ownerInfoForm, 'ownerPhone')"
                formControlName="ownerPhone"
                placeholder="05xxxxxxxxx">
              <small class="form-text text-muted">
                Son 4 hanesi geçici şifre olarak kullanılacaktır
              </small>
              <div class="invalid-feedback" *ngIf="isFieldInvalid(ownerInfoForm, 'ownerPhone')">
                <div *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('required')">
                  Telefon numarası zorunludur
                </div>
                <div *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('pattern')">
                  Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                </div>
              </div>
            </div>
          </div>

          <!-- Owner Email -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                E-posta Adresi <span class="text-danger">*</span>
              </label>
              <input
                type="email"
                class="modern-form-control"
                [class.is-invalid]="isFieldInvalid(ownerInfoForm, 'ownerEmail')"
                formControlName="ownerEmail"
                placeholder="<EMAIL>">
              <div class="invalid-feedback" *ngIf="isFieldInvalid(ownerInfoForm, 'ownerEmail')">
                <div *ngIf="ownerInfoForm.get('ownerEmail')?.hasError('required')">
                  E-posta adresi zorunludur
                </div>
                <div *ngIf="ownerInfoForm.get('ownerEmail')?.hasError('email')">
                  Geçerli bir e-posta adresi giriniz
                </div>
              </div>
            </div>
          </div>

          <!-- Send Welcome Email -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  formControlName="sendWelcomeEmail"
                  id="sendWelcomeEmail">
                <label class="form-check-label" for="sendWelcomeEmail">
                  Hoş geldin maili gönder (şimdilik console'a log)
                </label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 4: Preview -->
  <div *ngIf="currentStep === SalonWizardStep.PREVIEW" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faCheck" class="me-2"></fa-icon>
        Önizleme ve Kaydet
      </h5>
      <p class="mb-0 mt-2 text-muted">
        <small>Bilgileri kontrol edin ve kaydetmek için onaylayın</small>
      </p>
    </div>
    <div class="modern-card-body">
      <div class="row g-4">
        <!-- Company Info Preview -->
        <div class="col-md-4">
          <div class="preview-section">
            <h6 class="preview-title">
              <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
              Şirket Bilgileri
            </h6>
            <div class="preview-content">
              <p><strong>Şirket Adı:</strong> {{ salonData.companyName }}</p>
              <p><strong>Telefon:</strong> {{ salonData.companyPhone }}</p>
            </div>
          </div>
        </div>

        <!-- Address Info Preview -->
        <div class="col-md-4">
          <div class="preview-section">
            <h6 class="preview-title">
              <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
              Adres Bilgileri
            </h6>
            <div class="preview-content">
              <p><strong>İl/İlçe:</strong> {{ salonData.cityName }}/{{ salonData.townName }}</p>
              <p><strong>Adres:</strong> {{ salonData.address }}</p>
            </div>
          </div>
        </div>

        <!-- Owner Info Preview -->
        <div class="col-md-4">
          <div class="preview-section">
            <h6 class="preview-title">
              <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
              Şirket Sahibi
            </h6>
            <div class="preview-content">
              <p><strong>Ad Soyad:</strong> {{ salonData.ownerFullName }}</p>
              <p><strong>Telefon:</strong> {{ salonData.ownerPhone }}</p>
              <p><strong>E-posta:</strong> {{ salonData.ownerEmail }}</p>
              <p><strong>Hoş geldin maili:</strong> {{ salonData.sendWelcomeEmail ? 'Evet' : 'Hayır' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Important Notes -->
      <div class="alert alert-info mt-4">
        <h6><fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>Önemli Bilgiler:</h6>
        <ul class="mb-0">
          <li>Şirket sahibi için otomatik kullanıcı hesabı oluşturulacaktır</li>
          <li>Geçici şifre telefon numarasının son 4 hanesi olacaktır: <strong>{{ salonData.ownerPhone?.slice(-4) }}</strong></li>
          <li>İlk girişte şifre değiştirme zorunlu olacaktır</li>
          <li>Tüm işlemler transaction içinde gerçekleştirilecektir</li>
        </ul>
      </div>
    </div>
  </div>
</div>
