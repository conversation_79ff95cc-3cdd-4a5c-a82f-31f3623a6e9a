-- Salon Wizard Sistemi için gerekli tablo kontrolleri ve indeksler
-- Bu script'i canlıya aktarırken çalıştırın

-- 1. User tablosunda RequirePasswordChange alanının varlığını kontrol et
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'RequirePasswordChange')
BEGIN
    ALTER TABLE Users
    ADD RequirePasswordChange BIT NOT NULL DEFAULT 0;
    
    PRINT 'RequirePasswordChange alanı Users tablosuna eklendi.';
END
ELSE
BEGIN
    PRINT 'RequirePasswordChange alanı zaten mevcut.';
END

-- 2. Performans için indeksler oluştur
-- Company tablosu için indeksler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Companies_PhoneNumber')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Companies_PhoneNumber
    ON Companies (PhoneNumber)
    WHERE IsActive = 1;
    
    PRINT 'Companies.PhoneNumber indeksi oluşturuldu.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Companies_CompanyName')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Companies_CompanyName
    ON Companies (CompanyName)
    WHERE IsActive = 1;
    
    PRINT 'Companies.CompanyName indeksi oluşturuldu.';
END

-- CompanyUser tablosu için indeksler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyUsers_Email')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CompanyUsers_Email
    ON CompanyUsers (Email)
    WHERE IsActive = 1;
    
    PRINT 'CompanyUsers.Email indeksi oluşturuldu.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyUsers_PhoneNumber')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CompanyUsers_PhoneNumber
    ON CompanyUsers (PhoneNumber)
    WHERE IsActive = 1;
    
    PRINT 'CompanyUsers.PhoneNumber indeksi oluşturuldu.';
END

-- User tablosu için indeksler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Users_Email
    ON Users (Email)
    WHERE IsActive = 1;
    
    PRINT 'Users.Email indeksi oluşturuldu.';
END

-- UserCompany tablosu için indeksler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserCompany_UserID_CompanyId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_UserCompany_UserID_CompanyId
    ON UserCompany (UserID, CompanyId)
    WHERE IsActive = 1;
    
    PRINT 'UserCompany.UserID_CompanyId indeksi oluşturuldu.';
END

-- 3. Benzersizlik kontrolleri için unique indeksler
-- Company telefon numarası benzersizliği
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_Companies_PhoneNumber_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX UQ_Companies_PhoneNumber_Active
    ON Companies (PhoneNumber)
    WHERE IsActive = 1;
    
    PRINT 'Companies.PhoneNumber unique indeksi oluşturuldu.';
END

-- CompanyUser email benzersizliği
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_CompanyUsers_Email_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX UQ_CompanyUsers_Email_Active
    ON CompanyUsers (Email)
    WHERE IsActive = 1;
    
    PRINT 'CompanyUsers.Email unique indeksi oluşturuldu.';
END

-- CompanyUser telefon benzersizliği
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_CompanyUsers_PhoneNumber_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX UQ_CompanyUsers_PhoneNumber_Active
    ON CompanyUsers (PhoneNumber)
    WHERE IsActive = 1;
    
    PRINT 'CompanyUsers.PhoneNumber unique indeksi oluşturuldu.';
END

-- User email benzersizliği
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_Users_Email_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX UQ_Users_Email_Active
    ON Users (Email)
    WHERE IsActive = 1;
    
    PRINT 'Users.Email unique indeksi oluşturuldu.';
END

-- UserCompany ilişki benzersizliği (bir şirket sadece bir kullanıcıya bağlı)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_UserCompany_CompanyId_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX UQ_UserCompany_CompanyId_Active
    ON UserCompany (CompanyId)
    WHERE IsActive = 1;
    
    PRINT 'UserCompany.CompanyId unique indeksi oluşturuldu.';
END

-- 4. Performans için composite indeksler
-- CompanyAdress için şehir/ilçe aramaları
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyAdress_CityID_TownID')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CompanyAdress_CityID_TownID
    ON CompanyAdress (CityID, TownID)
    WHERE IsActive = 1;
    
    PRINT 'CompanyAdress.CityID_TownID indeksi oluşturuldu.';
END

-- 5. Salon listesi için view oluştur (performans optimizasyonu)
IF OBJECT_ID('vw_SalonDetails', 'V') IS NOT NULL
    DROP VIEW vw_SalonDetails;
GO

CREATE VIEW vw_SalonDetails AS
SELECT 
    c.CompanyID,
    c.CompanyName,
    c.PhoneNumber as CompanyPhone,
    c.IsActive as CompanyIsActive,
    c.CreationDate as CompanyCreationDate,
    
    ca.CompanyAdressID,
    ca.Adress as CompanyAddress,
    city.CityName,
    town.TownName,
    
    cu.CompanyUserID,
    cu.Name as OwnerName,
    cu.PhoneNumber as OwnerPhone,
    cu.Email as OwnerEmail,
    
    uc.UserCompanyID,
    uc.UserID,
    
    u.FirstName as UserFirstName,
    u.LastName as UserLastName,
    u.RequirePasswordChange
    
FROM Companies c
LEFT JOIN CompanyAdress ca ON c.CompanyID = ca.CompanyID AND ca.IsActive = 1
LEFT JOIN Cities city ON ca.CityID = city.CityID
LEFT JOIN Towns town ON ca.TownID = town.TownID
LEFT JOIN UserCompany uc ON c.CompanyID = uc.CompanyId AND uc.IsActive = 1
LEFT JOIN Users u ON uc.UserID = u.UserID AND u.IsActive = 1
LEFT JOIN CompanyUsers cu ON c.CompanyID = cu.CompanyUserID AND cu.IsActive = 1
WHERE c.IsActive = 1;
GO

PRINT 'vw_SalonDetails view oluşturuldu.';

-- 6. Stored procedure oluştur (opsiyonel - gelecekte kullanım için)
IF OBJECT_ID('sp_GetSalonStatistics', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetSalonStatistics;
GO

CREATE PROCEDURE sp_GetSalonStatistics
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        COUNT(*) as TotalSalons,
        COUNT(CASE WHEN u.RequirePasswordChange = 1 THEN 1 END) as PendingPasswordChanges,
        COUNT(CASE WHEN c.CreationDate >= DATEADD(DAY, -30, GETDATE()) THEN 1 END) as NewSalonsLast30Days,
        COUNT(DISTINCT ca.CityID) as UniqueCities
    FROM Companies c
    LEFT JOIN UserCompany uc ON c.CompanyID = uc.CompanyId AND uc.IsActive = 1
    LEFT JOIN Users u ON uc.UserID = u.UserID AND u.IsActive = 1
    LEFT JOIN CompanyAdress ca ON c.CompanyID = ca.CompanyID AND ca.IsActive = 1
    WHERE c.IsActive = 1;
END
GO

PRINT 'sp_GetSalonStatistics stored procedure oluşturuldu.';

PRINT 'Salon Wizard Sistemi migration tamamlandı!';
