using DataAccess.Concrete.EntityFramework;
using Entities.DTOs;
using FluentValidation;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class SalonCreateValidator : AbstractValidator<SalonCreateDto>
    {
        public SalonCreateValidator()
        {
            // Şirket Bilgileri Validasyonu
            RuleFor(s => s.CompanyName)
                .NotEmpty().WithMessage("Şirket adı boş bırakılamaz.")
                .MinimumLength(2).WithMessage("Şirket adı en az 2 karakter olmalıdır.")
                .MaximumLength(100).WithMessage("Şirket adı en fazla 100 karakter olabilir.")
                .Must(BeUniqueCompanyName).WithMessage("Bu şirket adı sistemde zaten kayıtlı.");

            RuleFor(s => s.CompanyPhone)
                .NotEmpty().WithMessage("Şirket telefon numarası boş bırakılamaz.")
                .Length(11).WithMessage("Telefon numarasını kontrol ediniz.")
                .Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır")
                .Must(BeUniqueCompanyPhone).WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");

            // Adres Bilgileri Validasyonu
            RuleFor(s => s.CityId)
                .NotEmpty().WithMessage("İl seçimi boş bırakılamaz.")
                .GreaterThan(0).WithMessage("Geçerli bir il seçiniz.");

            RuleFor(s => s.TownId)
                .NotEmpty().WithMessage("İlçe seçimi boş bırakılamaz.")
                .GreaterThan(0).WithMessage("Geçerli bir ilçe seçiniz.");

            RuleFor(s => s.Address)
                .NotEmpty().WithMessage("Adres boş bırakılamaz.")
                .MinimumLength(10).WithMessage("Adres en az 10 karakter olmalıdır.")
                .MaximumLength(500).WithMessage("Adres en fazla 500 karakter olabilir.");

            // Şirket Sahibi Bilgileri Validasyonu
            RuleFor(s => s.OwnerFullName)
                .NotEmpty().WithMessage("Şirket sahibi adı boş bırakılamaz.")
                .MinimumLength(3).WithMessage("Ad soyad en az 3 karakter olmalıdır.")
                .MaximumLength(100).WithMessage("Ad soyad en fazla 100 karakter olabilir.")
                .Must(HaveAtLeastTwoWords).WithMessage("Ad ve soyad en az 2 kelime olmalıdır.");

            RuleFor(s => s.OwnerPhone)
                .NotEmpty().WithMessage("Şirket sahibi telefon numarası boş bırakılamaz.")
                .Length(11).WithMessage("Telefon numarasını kontrol ediniz.")
                .Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır")
                .Must(BeUniqueOwnerPhone).WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");

            RuleFor(s => s.OwnerEmail)
                .NotEmpty().WithMessage("E-posta adresi boş bırakılamaz.")
                .EmailAddress().WithMessage("Geçerli bir e-posta adresi giriniz.")
                .Must(BeUniqueEmail).WithMessage("Bu e-posta adresi sistemde zaten kayıtlı.");
        }

        private bool BeUniqueCompanyName(string companyName)
        {
            using (var context = new GymContext())
            {
                return !context.Companies.Any(c => 
                    c.CompanyName == companyName && 
                    c.IsActive == true);
            }
        }

        private bool BeUniqueCompanyPhone(string phoneNumber)
        {
            using (var context = new GymContext())
            {
                return !context.Companies.Any(c => 
                    c.PhoneNumber == phoneNumber && 
                    c.IsActive == true);
            }
        }

        private bool BeUniqueOwnerPhone(string phoneNumber)
        {
            using (var context = new GymContext())
            {
                return !context.CompanyUsers.Any(cu => 
                    cu.PhoneNumber == phoneNumber && 
                    cu.IsActive == true);
            }
        }

        private bool BeUniqueEmail(string email)
        {
            using (var context = new GymContext())
            {
                // Hem User tablosunda hem CompanyUser tablosunda kontrol et
                var userExists = context.Users.Any(u => 
                    u.Email == email && 
                    u.IsActive == true);
                
                var companyUserExists = context.CompanyUsers.Any(cu => 
                    cu.Email == email && 
                    cu.IsActive == true);

                return !userExists && !companyUserExists;
            }
        }

        private bool StartsWithZero(string phoneNumber)
        {
            return !string.IsNullOrEmpty(phoneNumber) && phoneNumber.StartsWith("0");
        }

        private bool HaveAtLeastTwoWords(string fullName)
        {
            if (string.IsNullOrWhiteSpace(fullName))
                return false;

            var words = fullName.Trim().Split(' ', System.StringSplitOptions.RemoveEmptyEntries);
            return words.Length >= 2;
        }
    }
}
