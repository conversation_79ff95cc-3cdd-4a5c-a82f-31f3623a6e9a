export interface SalonCreateDto {
  // Şirket Bilgileri
  companyName: string;
  companyPhone: string;
  
  // Adres Bilgileri
  cityId: number;
  townId: number;
  address: string;
  
  // Şirket Sahibi Bilgileri
  ownerFullName: string;
  ownerPhone: string;
  ownerEmail: string;
  
  // Ayarlar
  sendWelcomeEmail: boolean;
}

// Wizard adımları için enum
export enum SalonWizardStep {
  COMPANY_INFO = 1,
  ADDRESS_INFO = 2,
  OWNER_INFO = 3,
  PREVIEW = 4
}

// Wizard step bilgileri
export interface WizardStepInfo {
  step: SalonWizardStep;
  title: string;
  description: string;
  icon: string;
}

export const SALON_WIZARD_STEPS: WizardStepInfo[] = [
  {
    step: SalonWizardStep.COMPANY_INFO,
    title: 'Şirket Bilgileri',
    description: 'Temel şirket bilgileri',
    icon: 'fas fa-building'
  },
  {
    step: SalonWizardStep.ADDRESS_INFO,
    title: '<PERSON><PERSON> Bilgileri',
    description: 'Lokasyon ve adres',
    icon: 'fas fa-map-marker-alt'
  },
  {
    step: SalonWizardStep.OWNER_INFO,
    title: 'Şirket Sahibi',
    description: 'Sahip bilgileri',
    icon: 'fas fa-user-tie'
  },
  {
    step: SalonWizardStep.PREVIEW,
    title: 'Önizleme',
    description: 'Kontrol ve kaydet',
    icon: 'fas fa-check'
  }
];
