/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  background-color: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-lg);
  color: var(--text-primary);
}

.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade in animation */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form styling - modern-components.css ile uyumlu */
.modern-form-group {
  margin-bottom: var(--spacing-md);
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.modern-form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.modern-form-control.is-invalid {
  border-color: var(--danger);
}

.modern-form-control.is-invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem var(--danger-light);
}

/* Material form field styling - Dark mode uyumlu */
.modern-mat-form-field {
  width: 100%;
}

::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
  color: var(--border-color) !important;
}

::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: var(--primary) !important;
}

::ng-deep .modern-mat-form-field .mat-form-field-label {
  color: var(--text-secondary) !important;
}

::ng-deep .modern-mat-form-field.mat-focused .mat-form-field-label {
  color: var(--primary) !important;
}

::ng-deep .modern-mat-form-field .mat-input-element {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

::ng-deep .modern-mat-form-field .mat-form-field-prefix {
  color: var(--primary) !important;
  margin-right: 8px;
}

::ng-deep .modern-mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

::ng-deep .modern-mat-form-field .mat-form-field-infix {
  border-top: 0;
  padding: 0.75rem 0;
}

/* Step navigation styling - modern-components.css ile uyumlu */
.btn {
  transition: all var(--transition-speed) var(--transition-timing);
  border-radius: var(--border-radius-md);
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn:disabled {
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

.btn:active {
  transform: translateY(0);
}

/* Preview section styling - modern-components.css ile uyumlu */
.preview-section {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  transition: all var(--transition-speed) var(--transition-timing);
  box-shadow: var(--shadow-sm);
}

.preview-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.preview-title {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 2px solid var(--primary-light);
  font-size: 1.1rem;
}

.preview-content p {
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  line-height: 1.6;
}

.preview-content p:last-child {
  margin-bottom: 0;
}

.preview-content strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* Alert styling - modern-components.css ile uyumlu */
.alert {
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.alert-info {
  background-color: var(--info-light);
  color: var(--info);
  border-left: 4px solid var(--info);
}

.alert h6 {
  color: var(--info);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.alert ul {
  margin-bottom: 0;
  padding-left: var(--spacing-md);
}

.alert li {
  margin-bottom: var(--spacing-xs);
  color: var(--info);
}

/* Form validation styling */
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

.form-check {
  padding-left: 1.5rem;
}

.form-check-input {
  margin-left: -1.5rem;
}

.form-check-label {
  color: var(--text-primary);
  cursor: pointer;
}

/* Small text styling */
.form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Responsive adjustments - Mobile first approach */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .modern-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .modern-card-header .d-flex {
    flex-direction: column;
    gap: var(--spacing-sm);
    width: 100%;
  }

  .preview-section {
    margin-bottom: var(--spacing-md);
  }

  .btn {
    font-size: 0.875rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }

  .modern-btn-icon {
    margin-right: var(--spacing-xs);
  }

  .modern-form-control {
    font-size: 16px; /* iOS zoom önleme */
  }
}

@media (max-width: 576px) {
  .row.g-2 .col {
    margin-bottom: var(--spacing-sm);
  }

  .btn.w-100 {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .btn.w-100 .d-flex {
    flex-direction: column;
    text-align: center;
  }

  .btn.w-100 .fw-bold {
    font-size: 0.9rem;
  }

  .btn.w-100 small {
    font-size: 0.7rem;
  }
}

/* Dark mode specific adjustments - CSS Variables kullanarak otomatik */
[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.8;
}

[data-theme="dark"] .preview-section {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .preview-section:hover {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .loading-spinner {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

[data-theme="dark"] .alert-info {
  background-color: rgba(var(--info-rgb), 0.1);
  border-color: var(--info);
}

[data-theme="dark"] .alert-info h6,
[data-theme="dark"] .alert-info li {
  color: var(--info);
}

/* Button hover effects */
.modern-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.modern-btn:active {
  transform: translateY(0);
}

/* Step button specific styling */
.btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

/* Icon spacing */
.modern-btn-icon {
  margin-right: var(--spacing-xs);
}

.modern-btn-icon:last-child {
  margin-right: 0;
  margin-left: var(--spacing-xs);
}

/* Step button specific styling - modern-components.css ile uyumlu */
.btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-secondary);
  background-color: transparent;
}

.btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--white);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-dark);
  border-color: var(--success-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Wizard step progress indicator */
.btn.w-100.text-start {
  text-align: left !important;
  justify-content: flex-start;
}

.btn.w-100.text-start .d-flex {
  align-items: center;
  width: 100%;
}

.btn.w-100.text-start .me-2 {
  flex-shrink: 0;
  width: 24px;
  text-align: center;
}

/* Animation for step transitions */
.step-transition {
  transition: all var(--transition-speed) var(--transition-timing);
}

/* Focus states for accessibility */
.btn:focus,
.modern-form-control:focus,
.form-check-input:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading state for buttons */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn.loading {
  position: relative;
  color: transparent !important;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
