using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class SalonCreateDto : IDto
    {
        // Şirket Bilgileri
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        
        // Adres Bilgileri
        public int CityId { get; set; }
        public int TownId { get; set; }
        public string Address { get; set; }
        
        // Şirket Sahibi Bilgileri
        public string OwnerFullName { get; set; }
        public string OwnerPhone { get; set; }
        public string OwnerEmail { get; set; }
        
        // Ayarlar
        public bool SendWelcomeEmail { get; set; } = false;
    }
}
