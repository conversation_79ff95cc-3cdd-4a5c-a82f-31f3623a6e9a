import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';
import { SalonCreateDto } from '../models/salon-create.model';

@Injectable({
  providedIn: 'root'
})
export class SalonService extends BaseApiService {
  
  constructor(private httpClient: HttpClient) {
    super();
  }

  createSalonWithOwner(salonCreateDto: SalonCreateDto): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'salon/create',
      salonCreateDto
    );
  }
}
