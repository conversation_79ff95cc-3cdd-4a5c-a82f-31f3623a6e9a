import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Observable, startWith, map } from 'rxjs';

// Font Awesome Icons
import { 
  faBuilding, faMapMarkerAlt, faUserTie, faCheck, 
  faArrowLeft, faArrowRight, faSave, faInfoCircle,
  faPhone, faEnvelope, faHome, faCity
} from '@fortawesome/free-solid-svg-icons';

// Services
import { SalonService } from '../../services/salon.service';
import { CityService } from '../../services/city.service';
import { TownService } from '../../services/town.service';

// Models
import { SalonCreateDto, SalonWizardStep, SALON_WIZARD_STEPS } from '../../models/salon-create.model';
import { City } from '../../models/city';
import { Town } from '../../models/town';

@Component({
  selector: 'app-salon-wizard-add',
  templateUrl: './salon-wizard-add.component.html',
  styleUrls: ['./salon-wizard-add.component.css'],
  standalone: false
})
export class SalonWizardAddComponent implements OnInit {
  // Icons
  faBuilding = faBuilding;
  faMapMarkerAlt = faMapMarkerAlt;
  faUserTie = faUserTie;
  faCheck = faCheck;
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faSave = faSave;
  faInfoCircle = faInfoCircle;
  faPhone = faPhone;
  faEnvelope = faEnvelope;
  faHome = faHome;
  faCity = faCity;

  // Wizard state
  currentStep: SalonWizardStep = SalonWizardStep.COMPANY_INFO;
  SalonWizardStep = SalonWizardStep; // Template'de kullanmak için
  wizardSteps = SALON_WIZARD_STEPS;

  // Forms
  companyInfoForm!: FormGroup;
  addressInfoForm!: FormGroup;
  ownerInfoForm!: FormGroup;
  isSubmitting = false;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  filteredCities!: Observable<City[]>;
  filteredTowns!: Observable<Town[]>;

  // Progress tracking
  completedSteps: Set<SalonWizardStep> = new Set();
  salonData: any = {};

  constructor(
    private fb: FormBuilder,
    private salonService: SalonService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.loadCities();
    this.loadTowns();
    this.setupAutocomplete();
  }

  initializeForms(): void {
    // Adım 1: Şirket bilgileri formu
    this.companyInfoForm = this.fb.group({
      companyName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });

    // Adım 2: Adres bilgileri formu
    this.addressInfoForm = this.fb.group({
      city: ['', [Validators.required]],
      town: ['', [Validators.required]],
      address: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]]
    });

    // Adım 3: Şirket sahibi bilgileri formu
    this.ownerInfoForm = this.fb.group({
      ownerFullName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100), this.fullNameValidator]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]],
      ownerEmail: ['', [Validators.required, Validators.email]],
      sendWelcomeEmail: [false]
    });
  }

  setupAutocomplete(): void {
    // Şehir autocomplete
    this.filteredCities = this.addressInfoForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(name => name ? this._filterCities(name) : this.cities.slice())
    );

    // İlçe autocomplete
    this.filteredTowns = this.addressInfoForm.get('town')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.townName),
      map(name => name ? this._filterTowns(name) : this.towns.slice())
    );
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      this.completedSteps.add(this.currentStep);
      
      if (this.currentStep < SalonWizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > SalonWizardStep.COMPANY_INFO) {
      this.currentStep--;
    }
  }

  goToStep(step: SalonWizardStep): void {
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  canGoToStep(step: SalonWizardStep): boolean {
    if (step === this.currentStep) return true;
    if (this.completedSteps.has(step)) return true;
    
    const previousStep = step - 1;
    if (previousStep >= SalonWizardStep.COMPANY_INFO) {
      return this.completedSteps.has(previousStep);
    }
    
    return step === SalonWizardStep.COMPANY_INFO;
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case SalonWizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        break;
      case SalonWizardStep.OWNER_INFO:
        this.saveAddressInfo();
        break;
      case SalonWizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case SalonWizardStep.COMPANY_INFO:
        return this.companyInfoForm.valid;
      case SalonWizardStep.ADDRESS_INFO:
        return this.addressInfoForm.valid;
      case SalonWizardStep.OWNER_INFO:
        return this.ownerInfoForm.valid;
      default:
        return true;
    }
  }

  // Data saving methods
  saveCompanyInfo(): void {
    if (this.companyInfoForm.valid) {
      this.salonData = {
        ...this.salonData,
        ...this.companyInfoForm.value
      };
    }
  }

  saveAddressInfo(): void {
    if (this.addressInfoForm.valid) {
      const formValue = this.addressInfoForm.value;
      this.salonData = {
        ...this.salonData,
        cityId: formValue.city.cityID,
        townId: formValue.town.townID,
        address: formValue.address,
        // Display için
        cityName: formValue.city.cityName,
        townName: formValue.town.townName
      };
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerInfoForm.valid) {
      this.salonData = {
        ...this.salonData,
        ...this.ownerInfoForm.value
      };
    }
  }

  preparePreviewData(): void {
    // Preview için tüm verileri hazırla
    console.log('Salon Data:', this.salonData);
  }

  // Submit method
  onSubmit(): void {
    if (!this.canSubmit()) {
      this.toastrService.error('Lütfen tüm adımları tamamlayınız', 'Eksik Bilgi');
      return;
    }

    this.isSubmitting = true;

    const salonCreateDto: SalonCreateDto = {
      companyName: this.salonData.companyName,
      companyPhone: this.salonData.companyPhone,
      cityId: this.salonData.cityId,
      townId: this.salonData.townId,
      address: this.salonData.address,
      ownerFullName: this.salonData.ownerFullName,
      ownerPhone: this.salonData.ownerPhone,
      ownerEmail: this.salonData.ownerEmail,
      sendWelcomeEmail: this.salonData.sendWelcomeEmail || false
    };

    this.salonService.createSalonWithOwner(salonCreateDto).subscribe({
      next: (result) => {
        if (result.success) {
          this.toastrService.success('Salon başarıyla oluşturuldu!', 'Başarılı');
          this.router.navigate(['/company/unified-add']);
        } else {
          this.toastrService.error(result.message || 'Salon oluşturulamadı', 'Hata');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastrService.error('Salon oluşturulurken hata oluştu', 'Hata');
        console.error('Salon creation error:', error);
        this.isSubmitting = false;
      }
    });
  }

  canSubmit(): boolean {
    return this.completedSteps.has(SalonWizardStep.COMPANY_INFO) &&
           this.completedSteps.has(SalonWizardStep.ADDRESS_INFO) &&
           this.completedSteps.has(SalonWizardStep.OWNER_INFO) &&
           this.ownerInfoForm.valid;
  }

  // Helper methods
  loadCities(): void {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  loadTowns(): void {
    this.townService.getTowns().subscribe((response) => {
      this.towns = response.data;
    });
  }

  private _filterCities(name: string): City[] {
    const filterValue = name.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(name: string): Town[] {
    const filterValue = name.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }

  // Validation methods
  validateAndHighlightErrors(): void {
    switch (this.currentStep) {
      case SalonWizardStep.COMPANY_INFO:
        this.markFormGroupTouched(this.companyInfoForm);
        break;
      case SalonWizardStep.ADDRESS_INFO:
        this.markFormGroupTouched(this.addressInfoForm);
        break;
      case SalonWizardStep.OWNER_INFO:
        this.markFormGroupTouched(this.ownerInfoForm);
        break;
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Step helper methods
  getStepButtonClass(step: SalonWizardStep): string {
    if (step === this.currentStep) {
      return 'btn-primary';
    } else if (this.completedSteps.has(step)) {
      return 'btn-success';
    } else {
      return 'btn-outline-secondary';
    }
  }

  getStepIcon(step: SalonWizardStep): any {
    if (this.completedSteps.has(step)) {
      return this.faCheck;
    }
    
    const stepInfo = this.wizardSteps.find(s => s.step === step);
    switch (stepInfo?.icon) {
      case 'fas fa-building': return this.faBuilding;
      case 'fas fa-map-marker-alt': return this.faMapMarkerAlt;
      case 'fas fa-user-tie': return this.faUserTie;
      case 'fas fa-check': return this.faCheck;
      default: return this.faInfoCircle;
    }
  }

  getStepTitle(): string {
    const stepInfo = this.wizardSteps.find(s => s.step === this.currentStep);
    return stepInfo?.title || '';
  }

  // Form field validation helpers
  isFieldInvalid(form: FormGroup, fieldName: string): boolean {
    const field = form.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Custom validator for full name (at least 2 words)
  fullNameValidator(control: any) {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const words = control.value.trim().split(' ').filter((word: string) => word.length > 0);
    if (words.length < 2) {
      return { 'fullName': { message: 'Ad ve soyad en az 2 kelime olmalıdır' } };
    }

    return null;
  }
}
